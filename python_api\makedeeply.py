import requests


class MakeDeeply:

	# TBD: document vs. HTTP_API_PORT in config.rs
	API_PORT = 44985  # TBD: avoid hardcoding, or at least let user change as needed
	API_HOST = '127.0.0.1'
	MAIN_WINDOW_LABEL = 'main'

	"""
	API for MakeDeeply desktop app
	"""
	def __init__(self, host = API_HOST, port = API_PORT) -> None:
		self.base_url = f'http://{host}:{port}'


	def create_window(self, url: str):
		"""
		create new window with the given URL
		returns label (id) for window
		"""
		resp = requests.post(f'{self.base_url}/create_window', json={
			'url': url
		})
		resp.raise_for_status()
		body = resp.json()
		label = body['label']
		return label


	def get_outer_html(self, label: str):
		"""
		get outer HTML for a given window label
		"""
		resp = requests.post(f'{self.base_url}/get_outer_html', json={
			'label': label
		})
		resp.raise_for_status()
		body = resp.json()
		html = body['html']
		return html


	def wait_for_document(self, label: str):
		"""
		wait for document to be ready
		"""
		resp = requests.post(f'{self.base_url}/wait_for_document', json={
			'label': label
		})
		resp.raise_for_status()
		body = resp.json()
		return body


	def set_visible(self, label: str, visible: bool):
		"""
		set window visibillity
		"""
		resp = requests.post(f'{self.base_url}/set_visible', json={
			'label': label,
			'visible': visible
		})
		resp.raise_for_status()
		body = resp.json()
		return body


	def is_focused(self, label: str):
		"""
		get window focused
		returns boolean
		"""
		resp = requests.post(f'{self.base_url}/is_focused', json={
			'label': label
		})
		resp.raise_for_status()
		body = resp.json()
		return body['is_focused']


	def set_focus(self, label: str, focus: bool):
		"""
		set window visibillity
		"""
		resp = requests.post(f'{self.base_url}/set_focus', json={
			'label': label,
			'focus': focus
		})
		resp.raise_for_status()
		body = resp.json()
		return body


	def close_window(self, label: str):
		"""
		close window
		"""
		resp = requests.post(f'{self.base_url}/close_window', json={
			'label': label,
		})
		resp.raise_for_status()
		body = resp.json()
		return body


	def get_location(self, label: str):
		"""
		get document.location for a given window label
		"""
		resp = requests.post(f'{self.base_url}/get_location', json={
			'label': label
		})
		resp.raise_for_status()
		body = resp.json()
		location = body['location']
		return location
