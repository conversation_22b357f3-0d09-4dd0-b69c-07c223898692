import { Compartment, RangeSet, StateEffect, StateField } from '@codemirror/state';
import { Editor<PERSON><PERSON><PERSON>, gutter, GutterMarker } from '@codemirror/view';
import { Editor } from '../editor';

let currentRTPos = null;
let currentRTLine = null;

const rightTabEffect = StateEffect.define({
	map: (val, mapping) => ({ pos: mapping.mapPos(val.pos), on: val.on }),
});

const rightTabState = StateField.define({
	create() {
		return RangeSet.empty;
	},
	update(set, transaction) {
		set = set.map(transaction.changes);
		for (let e of transaction.effects) {
			if (e.is(rightTabEffect)) {
				if (e.value.on) set = set.update({ add: [rightTabMarker.range(e.value.pos)] });
				else set = set.update({ filter: (from) => from != e.value.pos });
			}
		}
		return set;
	},
});

function showRightTab(view, pos) {
	if (!currentRTPos || currentRTPos != pos) {
		if (currentRTPos != pos) removeRightTab(view);
		currentRTPos = pos;
		currentRTLine = Editor.getLine(pos).number;
		console.log('Showing', currentRTPos, currentRTLine);
		view.dispatch({
			effects: rightTabEffect.of({ pos, on: true }),
		});
		startGutterListener(view);
	}
}

function removeRightTab(view) {
	if (currentRTPos != null) {
		console.log('Removing', currentRTPos);
		view.dispatch({
			effects: rightTabEffect.of({ pos: currentRTPos, on: false }),
		});
		currentRTPos = null;
		currentRTLine = null;
		removeGutterListener(view);
	}
}

const rightTabMarker = new (class extends GutterMarker {
	toDOM() {
		// Create an img element
		const img = document.createElement('img');
		// Set the src attribute
		img.setAttribute('src', '../../../assets/right-tab-arrow.svg');
		img.setAttribute('width', '16px');
		img.setAttribute('height', '16px');
		// Return the created img element
		return img;
	}
})();

// Selection listener Functions
let rightTabGutterListener = new Compartment();

function startGutterListener(view) {
	const ext = EditorView.updateListener.of((update) => {
		if (update.docChanged || update.selectionSet) {
			let newLine = update.view.state.doc.lineAt(update.view.state.selection.main.anchor).number;
			if (newLine != currentRTLine) removeRightTab(view);
		}
	});
	view.dispatch({
		effects: rightTabGutterListener.reconfigure(ext),
	});
}

function isRightTabGutter(view) {
	return rightTabGutterCompartment.get(view.state) ? true : false;
}

function removeGutterListener(view) {
	view.dispatch({
		effects: rightTabGutterListener.reconfigure(null),
	});
}

const rightTabGutter = [
	rightTabState,
	gutter({
		class: 'cm-right-tab-gutter',
		markers: (v) => v.state.field(rightTabState),
		initialSpacer: () => rightTabMarker,
	}),
];

const rightTabGutterCompartment = new Compartment();

function startRightTabGutter(view) {
	view.dispatch({
		effects: rightTabGutterCompartment.reconfigure(rightTabGutter),
	});
}

function removeRightTabGutter(view) {
	view.dispatch({
		effects: rightTabGutterCompartment.reconfigure(null),
	});
}

export const Gutters = {
	rightTabGutter,
	showRightTab,
	removeRightTab,
	rightTabGutterListener,
	rightTabGutterCompartment,
	startRightTabGutter,
	removeRightTabGutter,
};
