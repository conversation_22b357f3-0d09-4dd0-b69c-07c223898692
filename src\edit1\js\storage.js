import { getFileNameFromPath } from './file';

function setData(data) {
	window.label = data.label;
	sessionStorage.setItem(data.label, JSON.stringify(data.file));
}

function getData() {
	return JSON.parse(sessionStorage.getItem(window.label));
}
function setFile(file) {
	sessionStorage.setItem(window.label, JSON.stringify(file));
}

function getFileName() {
	let data = getData();
	if (data && data.path) {
		let fileName = getFileNameFromPath(data.path);
		console.log('File Name =>', fileName);
		return fileName;
	}
	return null;
}

async function getFileFolder() {
	let data = getData();
	if (data && data.path) {
		return data.path; //await invoke('get_file_folder', { path: data.path });
	}
	return null;
}

export const MyStorage = {
	setData,
	getData,
	setFile,
	getFileName,
	getFileFolder,
};
