import { view } from '../../main';
import { EditorManager } from '../editor-manager';
import { SplitScreen } from './split-screen';
import { SplitThumb } from './split-thumb-dragger.js';

export function startResizeListener() {
	const resizer = document.getElementById('splite-resizer');
	const editor1 = document.querySelector('#editor');
	const editor2 = document.querySelector('#editor2');
	let isResizing = false;
	let startY;
	let startHeight;

	resizer.addEventListener('mousedown', initResize);

	function initResize(e) {
		console.log('Init resize');
		isResizing = true;
		startY = e.clientY;
		startHeight = editor1.offsetHeight;

		// Add dragging class for visual feedback
		resizer.classList.add('dragging');

		// Add event listeners
		document.addEventListener('mousemove', resize);
		document.addEventListener('mouseup', stopResize);

		// Prevent text selection while dragging
		document.body.style.userSelect = 'none';
	}

	function resize(e) {
		if (!isResizing) return;

		const diff = e.clientY - startY;
		const newHeight = Math.max(50, startHeight + diff);
		console.log("🚀 ~ resize ~ newHeight:", newHeight)
		const containerHeight = document.getElementById('container').offsetHeight;
		console.log("🚀 ~ resize ~ containerHeight:", containerHeight)

		// Ensure editor2 maintains minimum height of 50px
		// if (containerHeight - newHeight - resizer.offsetHeight >= 32) {
			editor1.style.height = `${e.clientY}px`;
			editor1.style.flexGrow = 0; // Remove flex-grow when explicitly set height
			SplitThumb.updateSplitThumbPosition(newHeight)
		// }
	}

	function stopResize() {
		isResizing = false;
		resizer.classList.remove('dragging');
		document.removeEventListener('mousemove', resize);
		document.removeEventListener('mouseup', stopResize);
		document.body.style.userSelect = '';
		if (EditorManager.splitIsFocused()) SplitScreen.getSplitView().focus();
		else view.focus();
	}
}

export function stopResizing() {
	console.log('Removed');
	const editor1 = document.querySelector('#editor');
	editor1.removeAttribute('style');
}
