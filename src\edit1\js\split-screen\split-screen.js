// import { javascript } from '@codemirror/lang-javascript';
import { Annotation, Compartment, EditorState, Transaction } from '@codemirror/state';
import { EditorView } from '@codemirror/view';
import { getCurrentWindow } from '@tauri-apps/api/window';
import elt from 'crelt';
import { espresso } from 'thememirror';
import { view } from '../../main';
import { openSearchPanel } from '../codemirror-search-modified';
import { Editor, EditorController } from '../editor';
import { EditorManager } from '../editor-manager';
import { mySplitSetup } from './editor-setup';
import { startResizeListener, stopResizing } from './split-screen-resizer';
import { SplitThumb } from './split-thumb-dragger.js';
// import { SplitEditor } from './SplitEditor';
import { WindowHeader } from '../window-header.js';
import { SplitFindDialog } from './splitFindDialog';
const appWindow = getCurrentWindow();

let splitView = null;
// const splitBtn = document.getElementById('split-doc');
let splitViewDestroyer = null;
const splitListener = new Compartment();
export const SplitEditor = new EditorController();

function createSplitHtml() {
	document.getElementById('editor').classList.add('split');
	const splitEl = elt('div', { class: 'split-screen-container' }, [
		elt('div', { class: 'splite-resizer', id: 'splite-resizer', ondblclick: removeSplitEditor }, [
			elt('div', { class: 'editor-resizer' }),
		]),
		elt('div', { id: 'editor2' }),
	]);
	document.querySelector('#container').appendChild(splitEl);
}

function removeSplitHtml() {
	let element = document.querySelector('.split-screen-container');
	if (element) element.remove();
	document.getElementById('editor').classList.remove('split');
	view.focus();
}
let syncAnnotation = Annotation.define();

function syncDispatch(tr, view, other) {
	// Update the view that generated the transaction
	try {
		view.update([tr]);

		if (!tr.changes.empty && !tr.annotation(syncAnnotation)) {
			// Create an annotation array if needed
			let annotations = [syncAnnotation.of(true)];
			let userEvent = tr.annotation(Transaction.userEvent);
			console.log('User Event ===> ', userEvent);
			if (userEvent === 'select.search') {
				// Skip syncing this transaction
				return;
			}
			if (userEvent) annotations.push(Transaction.userEvent.of(userEvent));
			// Instead of dispatching the original transaction, build a new one for the other view
			// based on its current state
			const otherTr = other.state.update({
				changes: tr.changes,
				annotations,
			});
			other.dispatch(otherTr);
		}
	} catch (error) {
		console.log('SyncDispatch Error:', error);
	}
}

function adjustSplitScreenCursorPos(from, to) {
	if (splitView) {
		let trans = splitView.state.update({
			selection: { anchor: Math.min(from, to) },
			effects: EditorView.scrollIntoView(to, {
				y: 'start',
			}),
		});
		splitView.dispatch(trans);
	}
}

function setFocusColorEffect(hasFocus) {
	const editor = document.querySelector("#editor2");
	const gutters = document.querySelector('#editor2 .cm-gutters');
	const content = document.querySelector('#editor2 .cm-content');
	const searchMatches = document.querySelector('#editor2 .search-matches');
	console.log('Gutters =>', gutters);
	if (gutters) {
		if (hasFocus) {
			editor.classList.add('active');
			gutters.classList.remove('blured');
			content.classList.remove('blured');
			if (searchMatches) searchMatches.style.display = 'flex';
			EditorManager.setFocusedEditor('split');
		} else {
			editor.classList.remove('active');
			gutters.classList.add('blured');
			content.classList.add('blured');
			if (searchMatches) searchMatches.style.display = 'none';
		}
	}
}

function split(){
	// splitBtn.classList.add('split');
	createSplitHtml();

	const focusTrackerExtension = EditorView.updateListener.of((update) => {
		if (update.focusChanged) {
			const status = update.view.hasFocus;
			console.log(`Editor is now: ${status}`);
			if(status || Editor.isFocused()) {
				setFocusColorEffect(status);
				Editor.setFocusColorEffect(!status);
			}
		}
	});

	console.log('main editor value', view.state.doc);
	const splitState = EditorState.create({
		doc: view.state.doc,
		extensions: [
			mySplitSetup,
			SplitEditor.lineWrap.of(WindowHeader.getLineWrapState()?[EditorView.lineWrapping]:[]),
			SplitEditor.theme.of(espresso),
			// Editor.language.of(javascript()),
			SplitEditor.selectionListener.of([]),
			focusTrackerExtension,
		],
	});

	splitView = new EditorView({
		state: splitState,
		parent: document.querySelector('#editor2'),
		dispatch: (tr) => {
			console.log('from split', tr);
			syncDispatch(tr, splitView, view);
		},
	});

	SplitEditor.attachView(splitView);

	splitViewDestroyer = splitView.destroy;

	document.querySelector('#editor2 .cm-gutters')?.addEventListener('click', () => {
		splitView.focus();
	});

	const ext = EditorView.updateListener.of((update) => {
		splitView.dispatch({ changes: update.changes });
	});
	view.dispatch((tr) => syncDispatch(tr, view, splitView));
	view.dispatch({
		effects: splitListener.reconfigure(ext),
	});
	SplitFindDialog.initFinderListeners(splitView);
	// SplitEditor.setSplitEditorView(splitView);
	// let splitLine = view.state.doc.lineAt(view.state.doc.length).number + 1;
	// console.log('Split Line =', splitLine);

	const anchorPos = view.state.selection.main.anchor;
	const anchorLine = view.state.doc.lineAt(anchorPos);
	const { to } = view.state.doc.lineAt(anchorLine.to + 1);
	adjustSplitScreenCursorPos(to, to);
	let trans = view.state.update({
		scrollIntoView: true, //EditorSelection.single(from, to),
	});
	view.dispatch(trans);

	const isViewLineWrap = view.state.facet(EditorView.lineWrapping)
	if(isViewLineWrap) Editor.toggleLineWrapping(splitView)

	splitView.focus();
	setTimeout(() => {
		startResizeListener();
	}, 1500);
}



export function removeSplitEditor() {
	SplitThumb.setVisibility(true)
	SplitThumb.splitThumb.removeAttribute('style');
	// splitBtn.classList.remove('split');
	removeSplitHtml();
	if (splitListener) {
		view.dispatch({
			effects: splitListener.reconfigure(null),
		});
	}
	stopResizing();
	splitView = null;
}

function getSplitView() {
	return splitView;
}

async function openSearchPanelForSplitView(from, to) {
	if (splitView) {
		adjustSplitScreenCursorPos(from, to);
		await openSearchPanel(splitView);
	}
}

function isSplitViewFocused() {
	return splitView?splitView.hasFocus:false;
}


// splitBtn.addEventListener('click',(e)=>{
// 	Editor.setFocusColorEffect(false);
// 		const editor2 = document.getElementById('editor2');
// 		console.log("SPLIT ###",!editor2,e.payload);
// 		if (!editor2) {
// 			split()
// 		} else {
// 			removeSplitEditor();
// 		}
// })


export const SplitScreen = {
	SplitEditor,
	splitView,
	splitListener,
	syncDispatch,
	adjustSplitScreenCursorPos,
	getSplitView,
	openSearchPanelForSplitView,
	split,
	isSplitViewFocused,
	setFocusColorEffect
};

