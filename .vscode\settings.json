{
	"editor.insertSpaces": false,
	"editor.formatOnSave": false,
	"editor.codeActionsOnSave": {
		"source.organizeImports": "explicit",
		"source.fixAll": "explicit"
	},
	"rust-analyzer.check.command": "clippy",
	"rust-analyzer.checkOnSave": true,

	"prettier.ignorePath": ".prettierignore",
	"prettier.configPath": ".prettierrc.json",
	"javascript.preferences.importModuleSpecifierEnding": "js",
	"python.analysis.extraPaths": ["./src/common"], // Provide custom autocomplete
	"python.analysis.ignore": [
		"*" // <PERSON><PERSON><PERSON> does not have types
	]
}
