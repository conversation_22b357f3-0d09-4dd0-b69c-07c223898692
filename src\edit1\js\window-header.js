import { view } from "../main.js"
import { Editor } from "./editor.js"
import { SplitScreen } from "./split-screen/split-screen.js"

const windowHeader = document.querySelector(".window-header")
const lineWrapBtn = document.querySelector(".line-wrap-btn")

function giveFocus(e){
	const editor = document.querySelector("#editor")
	const editor2 = document.querySelector("#editor2")
	if(editor.classList.contains("active")){
		view.focus()
	} else if(editor2 && editor2.classList.contains("active")){
		SplitScreen.getSplitView().focus()
	}
}

windowHeader.addEventListener('mousedown',giveFocus);


let lineWrapState = true

function toggleLineWrapping(){
	Editor.setLineWrapping(lineWrapState)
	if(SplitScreen.SplitEditor.view){
		SplitScreen.SplitEditor.setLineWrapping(lineWrapState)
	}
	giveFocus()
}

lineWrapBtn.addEventListener("click",()=>{
	lineWrapState = !lineWrapState
	if(lineWrapState) lineWrapBtn.classList.add("active")
	else lineWrapBtn.classList.remove("active")
	toggleLineWrapping()
})

function getLineWrapState(){
	return lineWrapState;
}

export const  WindowHeader = {
	toggleLineWrapping,
	getLineWrapState
}

