import elt from 'crelt';

export const AlertType = {
	INFO: 'info',
	ERR: 'error',
	WARNING: 'warning',
	SUCCESS: 'success',
};

export class Alert {
	constructor(msg, type, duration = 0) {
		this.message = msg;
		this.type = type;
		this.duration = duration;
		this.alert = elt('div', { class: `alert ${this.type}` }, [
			elt('div', { class: 'alert-body' }, [
				elt('div', { class: 'body-left' }, [this.getIcon(), elt('p', { class: 'message' }, [this.message])]),
				elt('div', { class: 'close', onclick: () => this.closeAlert() }, [elt('i', { class: 'fa-solid fa-xmark' })]),
			]),
		]);

		if (duration > 0) {
			this.progress = elt('div', { class: 'progress' });
			this.alert.appendChild(elt('div', { class: 'progress-box' }, [this.progress]));
			this.progress.animate([{ width: '100%' }, { width: '0%' }], { duration: this.duration, easing: 'ease-out' }).onfinish = () => {
				this.closeAlert();
			};
		}

		this.alert = document.body.appendChild(this.alert);
	}

	getIcon() {
		let icons = {
			info: 'fa-solid fa-circle-info',
			error: 'fa-solid fa-circle-exclamation',
			warning: 'fa-solid fa-triangle-exclamation',
			success: 'fa-solid fa-circle-check',
		};
		return elt('div', { class: 'icon' }, [elt('i', { class: icons[this.type] })]);
	}

	closeAlert() {
		this.alert.parentNode.removeChild(this.alert);
	}
}
