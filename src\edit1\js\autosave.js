import { ask } from "@tauri-apps/plugin-dialog";
import { Editor } from "./editor.js";
import { MyStorage } from "./storage.js";
import { appWindow, invoke } from "/common/tauri.js";


async  function autosave(content){
	const file_path = MyStorage.getData()&& MyStorage.getData().path ? MyStorage.getData().path : undefined
console.log('Autosave',file_path,appWindow.label);
	await invoke('autosave_file', {
		originalPath: file_path ,
		name:appWindow.label,
		content,
	})
		.then((data) => {
			console.log('Autosave successful');
		})
		.catch((e) => console.error(e));
}


async  function updateIClose(close){
	const file_path = MyStorage.getData()&& MyStorage.getData().path ? MyStorage.getData().path : undefined
	await invoke('update_iclose_flag', {
		key: file_path ?? appWindow.label ,
		iclose:close,
	})
		.catch((e) => alert(e));
}

async  function removeAutosave(key){
	return await invoke('delete_autosave', {
		key,
	})
}

async  function checkAutosave(){
	const file_path = MyStorage.getData()&& MyStorage.getData().path ? MyStorage.getData().path : undefined
	if(file_path){
		const result =  await invoke('get_file_autosave', {
			key:file_path,
		})
		console.log("🚀 ~ checkAutosave ~ result:", result)
		if(result){
			const answer = await ask('Autosave found, do you want to load it?');
			if(answer){
				console.log('Autosave File',result);
				Editor.setValue(result.content);
				await removeAutosave(file_path);
			}else await removeAutosave(file_path);
		}
	}

}

async function initListeners(view){
	await appWindow.listen('editor:load-autosave', async (e) => {
		console.log('Autosave File',e.payload);
		Editor.setValue(e.payload.content);
		await removeAutosave(e.payload.autosave.original ? e.payload.autosave.original: e.payload.autosave.window_label);
	});
}


export const AutoSave = {
	autosave,
	updateIClose,
	removeAutosave,
	initListeners,
	checkAutosave
}
