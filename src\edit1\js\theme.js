import { Editor } from './editor';
import { event } from '/common/tauri.js';
let toggleTheme = document.getElementById('toggle-theme');

toggleTheme.addEventListener('click', async (e) => {
	document.body.classList.toggle('dark-theme');
	if (document.body.classList.contains('dark-theme')) {
		await event.emit('theme:change', { dark: true });
	} else {
		await event.emit('theme:change', { dark: false });
	}
	changeTheme(document.body.classList.contains('dark-theme'));
});

function changeTheme(dark){
	if (dark) {
		document.body.classList.add('dark-theme');
		toggleTheme.innerHTML = `<i class="fa-solid fa-sun"></i>`;
		toggleTheme.style.color = '#FCE570';
	} else {
		document.body.classList.remove('dark-theme');
		toggleTheme.innerHTML = `<i class="fa-solid fa-moon"></i>`;
		toggleTheme.style.color = '#132C2D';
	}
	Editor.setTheme(dark);
}

event.listen('theme:change', (e) => {
	changeTheme(e.payload.dark);
});
