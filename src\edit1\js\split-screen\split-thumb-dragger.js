import { SplitScreen } from "./split-screen.js";

const splitThumb = document.getElementById('split-thumb');
const topPanel = document.getElementById('editor');
const editor1 = document.querySelector('#editor');
const container = document.querySelector("#container")

let isDragging = false;
let visibility = true;

function setVisibility(value){
	splitThumb.style.visibility = value?'visible':'hidden';
}

splitThumb.addEventListener('mousedown', (e) => {
	e.preventDefault();
	isDragging = true;
	document.body.style.cursor = 'row-resize';


});

window.addEventListener('mousemove', (e) => {
	if (!isDragging) return;
	const newHeight = e.clientY;
	console.log('🚀 ~ window.addEventListener ~ newHeight:', newHeight);
	visibility = true;
	if (newHeight < 50 || newHeight > container.offsetHeight - 24) return;
	visibility = false;
	const editor2 = document.querySelector("#editor2")
	if(!editor2){
		editor1.style.height = `0px`;
		splitThumb.style.top = `15px`;
		editor1.style.flexGrow = 0;
		SplitScreen.split()
	}
	const dragBar = document.getElementById('splite-resizer');

	updateSplitThumbPosition(newHeight);
	topPanel.style.height = `${newHeight-15}px`;
	dragBar.style.top = `${newHeight-15}px`;
});

window.addEventListener('mouseup', () => {
	// splitThumb.style.top = `calc(60% - 7px)`;
	// splitThumb.style.visibility = `hidden`;
	if(isDragging) setVisibility(visibility);
	isDragging = false;
	document.body.style.cursor = 'default';
});

function updateSplitThumbPosition(newPos) {
	splitThumb.style.top = `${newPos}px`;
}


export const SplitThumb = {
	splitThumb,
	updateSplitThumbPosition,
	setVisibility
}
