// Unquotes backslash-escaped characters in a string
export function unquote(text) {
	const escapeChars = {
		n: '\n',
		r: '\r',
		t: '\t',
		'\\': '\\',
	};
	return text.replace(/\\([nrt\\])/g, (_, ch) => escapeChars[ch] || ch);
}

// Replaces placeholders in a string with corresponding match groups
export function getReplacement(result, replace) {
	return unquote(replace).replace(/\$(\$|&|\d+)/g, (m, i) => {
		if (i === '$') return '$';
		if (i === '&') return result[0];
		const index = parseInt(i, 10);
		if (!isNaN(index) && index > 0 && index < result.length) {
			return result[index];
		}
		return m; // No replacement found, return the original match
	});
}
