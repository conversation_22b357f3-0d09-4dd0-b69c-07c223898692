import { Editor } from './editor';
import { MyStorage } from './storage';
import { sep } from '@tauri-apps/api/path';

export function checkIfSaved() {
	if (Editor.getValue() == MyStorage.getData().content) {
		document.getElementById('save-state').style.backgroundColor = '#8f8';
	} else {
		document.getElementById('save-state').style.backgroundColor = '#f88';
	}
}

export function getFileNameFromPath(path) {
	const pathComponents = path.split(sep());
	const file_name = pathComponents.pop();
	return file_name;
}
