import { invoke } from '@tauri-apps/api/core';
import { getCurrentWebviewWindow, WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { listen, emit } from '@tauri-apps/api/event';
import { Editor } from './editor';
import { view } from '../main';
import { MyStorage } from './storage';
import { openSearchPanel, searchPanelOpen, SearchQuery } from './codemirror-search-modified';
import { Window } from '@tauri-apps/api/window';
const appWindow = getCurrentWebviewWindow();
// import { SearchQuery } from '@codemirror/search';

let data = {
	title: '',
	file_name: '',
	file_folder: '',
	parent_folder: '',
	window: appWindow.label,
	opendocs: false,
	finder: {
		find: '',
		replace: '',
		case_sensitive: false,
		entire_word: false,
		grep: false,
		selected_only: false,
	},
	selection: { from: 0, to: 0 },
	rows: [],
};

let matches = [];
let tabulaters = [];

async function getTabulaterTitle() {
	let doctitle = MyStorage.getFileName() ?? 'untitled';
	let docfolder = await MyStorage.getFileFolder();
	let baseTitle = `Tabulate from ${doctitle} ${docfolder ? ` of ${docfolder}` : ''}`;
	let title = baseTitle;
	let counter = 2;

	while (tabulaters.includes(title)) {
		title = `${baseTitle} (${counter})`;
		counter++;
	}
	tabulaters.push(title);

	return title;
}

async function openTabulater(openDocs = false) {
	data.title = openDocs ? 'Multi-File Find/Replace' : await getTabulaterTitle();
	data.opendocs = openDocs;
	await invoke('open_tabulater', { label: appWindow.label, data });
}

function tabulate() {
	let rows = [];
	if (matches.length > 0) {
		matches.forEach((m) => {
			delete m.match;
			let line = Editor.getLine(m.from);
			let found = view.state.doc.slice(m.from, m.to).toString();
			console.log('Found', found);
			rows.push({
				m,
				number: line.number,
				match_from: m.from - line.from,
				match_to: m.from - line.from + (m.to - m.from),
				content: line.text,
				found,
			});
		});
		console.log('Rows', rows);
	}
	if (rows.length > 0) {
		data.rows = rows;
		return true;
	} else return false;
}

async function initData(d) {
	data.finder = d;
	data.file_name = MyStorage.getFileName() ?? 'untitled';
	data.file_folder = (await MyStorage.getFileFolder()) ?? 'unsaved';
}

function getAllMatches() {
	console.log('Finder', data.finder);
	return new SearchQuery({
		search: data.finder.find,
		case_sensitive: data.finder.case_sensitive,
		regexp: data.finder.grep,
		replace: data.finder.replace,
		wholeWord: data.finder.entire_word,
	})
		.create()
		.matchAll(view.state);
}

async function displayMatches(which, label) {
	matches = getAllMatches();
	console.log('Matches', matches);
	if (matches.length > 0) {
		tabulate();
		let { from, to } = view.state.selection.main;
		data.selection = { from, to };
		console.log('Data', data);
		if (which == 'open') {
			await openTabulater(label == appWindow.label);
		} else {
			// add, refresh
			await new Window(label /* d.windowLabel */).emit(`tabulater:${which}-data`, data);
		}
	}
}

function initListeners() {
	listen('tabulater:tabulate', async (d) => {
		await initData(d.payload);
		await displayMatches('open', d.windowLabel);
	});

	listen('tabulater:refresh', async (d) => {
		await initData(d.payload.finder);
		await displayMatches('refresh', d.windowLabel);
	});

	listen('tabulater:get-data', async (d) => {
		console.log('tabulater:get-data', d);
		await initData(d.payload.finder);
		await displayMatches('add', d.windowLabel);
	});
}

export const FindTable = { initListeners, openTabulater };
