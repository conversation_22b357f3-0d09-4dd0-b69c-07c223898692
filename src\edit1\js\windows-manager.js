import { TauriEvent, listen } from '@tauri-apps/api/event';
import { exit } from '@tauri-apps/plugin-process';

let windows = ['main'];

listen(TauriEvent.WINDOW_CREATED, (e) => {
	windows.push(e.payload.label);
});

listen(TauriEvent.WINDOW_DESTROYED, async (e) => {
	windows = windows.filter((w) => w != e.windowLabel);
	if (windows.length <= 0) await exit(0);
});

export const WindowManager = {
	windows,
};
