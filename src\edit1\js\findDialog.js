import { invoke } from '@tauri-apps/api/core';
import { emit } from '@tauri-apps/api/event';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';
import { view } from '../main';
import { FinderStore } from '../ui/find-dialog/js/storage';
import { Editor } from './editor';

import { EditorSelection } from '@codemirror/state';
import { Window } from '@tauri-apps/api/window';
import { AlertType } from './alert';
import {
	countMatches,
	findFirst,
	findNext,
	findPrevious,
	getAllMatchesInDoc,
	getCountSuffix,
	openSearchPanel,
	replace,
	replaceAll,
	replaceNext,
	searchPanelOpen,
	setNewSearchQuery,
} from './codemirror-search-modified';
import { EditorManager } from './editor-manager';
import { FindTable } from './findTable';
import { SplitScreen } from './split-screen/split-screen';
import { MyStorage } from './storage';
const appWindow = getCurrentWebviewWindow();

let data = {
	find: '',
	replace: '',
	case_sensitive: false,
	entire_word: false,
	grep: true,
	showMatches: true,
	selected_only: false,
	wrapAround: false,
};

let currentSel = undefined;
let selection = undefined;
let lastAnchor = 0;
let countSuffix = undefined;

// like a constructure
(async () => {
	let d = await FinderStore.getFinderData();
	if (d != null) {
		data = d;
		//find();
	}
})();

async function openFinder() {
	let { from, to } = view.state.selection.main;
	lastAnchor = from == to ? from : lastAnchor;
	console.log('openFinder from cmd-f: from, to, lastAnchor', from, to, lastAnchor);
	// SplitScreen.adjustSplitScreenCursorPos(from, to);
	await openSearchPanel(view);
	await SplitScreen.openSearchPanelForSplitView(from, to);
	await invoke('open_finder', { label: appWindow.label });
}

const CountSuffixEnum = Object.freeze({
	TO_START: Symbol('to start'),
	TO_END: Symbol('to end'),
	IN_SELECTION: Symbol('in selection'),
	IN_DOC: Symbol('in document'),
});

function updateCountSuffix() {
	if (data.selected_only) {
		return CountSuffixEnum.IN_SELECTION.description;
	} else if (data.wrapAround) {
		return CountSuffixEnum.IN_DOC.description;
	} else {
		if (currentSel) {
			if (currentSel.to == view.state.doc.length) {
				return CountSuffixEnum.TO_END.description;
			} else {
				return CountSuffixEnum.TO_START.description;
			}
		} else return CountSuffixEnum.TO_END.description;
	}
}

async function setQueryIfClosed() {
	console.log('setQueryIfClosed');
	if (!searchPanelOpen(view.state)) {
		await openSearchPanel(view);
		await commit();
		view.focus();
	}
}

async function commit() {
	countSuffix = updateCountSuffix(); // update the global
	// await appWindow.emit('search-panel:find', {
	// 	search: data.find,
	// 	case_sensitive: data.case_sensitive,
	// 	regexp: data.grep,
	// 	replace: data.replace,
	// 	wholeWord: data.entire_word,
	// 	selection: data.selected_only ? selection : currentSel,
	// 	wrapAround: data.wrapAround,
	// 	anchor: lastAnchor,
	// 	selText: data.selected_only,
	// 	countSuffix: countSuffix,
	// });
	await setNewSearchQuery(view, {
		search: data.find,
		case_sensitive: data.case_sensitive,
		regexp: data.grep,
		replace: data.replace,
		wholeWord: data.entire_word,
		selection: data.selected_only ? selection : currentSel,
		wrapAround: data.wrapAround,
		anchor: lastAnchor,
		selText: data.selected_only,
		countSuffix: countSuffix,
	});
}

async function goNext() {
	console.log('Next main focused =>', EditorManager.mainIsFocused(), EditorManager.getFocusedEditor());
	if (EditorManager.mainIsFocused()) {
		let { from, to } = view.state.selection.main;
		// let { from2, to2 } = view.state.selection.main;
		console.log(from, to, '<-- goNext -->', view.state.selection.main);
		// SplitScreen.adjustSplitScreenCursorPos(from, to);

		if (!data.wrapAround && !data.selected_only) {
			let { from, to } = view.state.selection.main;
			lastAnchor = Math.max(from, to);
			currentSel = { from: lastAnchor, to: view.state.doc.length };
			countSuffix = CountSuffixEnum.TO_END.description;
			await commit();
		}
		findNext(view); // codemirror standard behavior
	}
}

async function goPrevious() {
	if (!data.wrapAround && !data.selected_only) {
		let { from, to } = view.state.selection.main;
		lastAnchor = Math.max(from, to);
		currentSel = { from: 0, to: view.state.selection.main.anchor };
		countSuffix = CountSuffixEnum.TO_START.description;
		await commit();
	}
	findPrevious(view); // codemirror standard behavior

	let { from, to } = view.state.selection.main;
	console.log(from, to, '<** goPrev **>', view.state.selection.main);
}

function startWrapFalseListener() {
	Editor.startSelectionListener(async (t) => {
		console.log('****************** heard selection (WrapAround) ******************', t);
		let { from, to } = view.state.selection.main;
		if (countSuffix == CountSuffixEnum.TO_START.description) {
			currentSel = { from: 0, to: Math.max(from, to) };
		} else {
			currentSel = { from: view.state.selection.main.anchor, to: view.state.doc.length };
		}
		await appWindow.emit('finder:commit');

		setTimeout(() => countMatches(view), 1000);
	});
}

function startSelectionTrueListener() {
	let { from, to } = view.state.selection.main;
	selection = { from, to };
	Editor.startSelectionListener(async (t) => {
		console.log('****************** heard selection ******************', t);
		if (t.filter((e) => e.isUserEvent('select')).length > 0) {
			await appWindow.emit('finder:find', data);
		}
		setTimeout(() => countMatches(view), 1000);
	});
}

function runListenerIfNeeded() {
	if (data.selected_only) startSelectionTrueListener();
	else if (!data.wrapAround) startWrapFalseListener();
}

function initFinderListeners() {
	//execute whene the find value changed in the finder dialog
	appWindow.listen('finder:find', async (d) => {
		data = d.payload;

		console.log('>>>>>>>', data);
		let { from, to } = view.state.selection.main;
		console.log('heard finder:find - from, to, data.selected_only', from, to, data.selected_only);

		// set current selection if there is one
		if (data.selected_only) {
			console.log('SELECTION', selection);
			startSelectionTrueListener();
		} else if (!data.selected_only && data.wrapAround) {
			Editor.removeSelectionListener();
			currentSel = { from: 0, to: view.state.doc.length };
		} else if (!data.selected_only) Editor.removeSelectionListener();

		lastAnchor = from == to ? from : lastAnchor;
		if (!data.wrapAround && !data.selected_only) {
			currentSel = { from: from, to: view.state.doc.length };
			startWrapFalseListener();
		}
		//create a new search query and findFirst if grep or find value changed from the old query
		// else juste update the options of the current query
		await commit();
		// if(data.selected_only) selectMatches(view);
	});

	appWindow.listen('finder:commit', async () => {
		await commit();
	});

	new Window("search").listen('finder:next', async () => {
		console.log("######## NEXT ########");
		await goNext();
		countMatches(view);
	});

	appWindow.listen('finder:previous', async () => {
		await goPrevious();
		countMatches(view);
	});

	appWindow.listen('finder:first', () => {
		findFirst(view);
	});

	appWindow.listen('finder:replace-value', async (d) => {
		data.replace = d.payload;
		await commit();
	});

	appWindow.listen('finder:replace', () => {
		replace(view);
		countMatches(view);
	});

	appWindow.listen('finder:replace-all', () => {
		replaceAll(view);
		countMatches(view);
	});

	appWindow.listen('finder:count-matches', () => {
		countMatches(view);
	});

	appWindow.listen('finder:open-panel', async () => {
		console.log('finder:open-panel');
		if (!searchPanelOpen(view.state)) {
			await openSearchPanel(view);
			await appWindow.emit('finder:find', data);
		}
	});

	appWindow.listen('finder:closed', async () => {
		await appWindow.setFocus();
		view.focus();
	});

	appWindow.listen('finder:go-to', async (d) => {
		console.log('finder:go-to');
		if (EditorManager.mainIsFocused()) {
			if (!searchPanelOpen(view.state)) await openSearchPanel(view);
			await appWindow.setFocus();
			view.focus();
			let { from, to } = d.payload;
			view.dispatch({
				selection: EditorSelection.single(from, to),
				scrollIntoView: true,
			});
		}
	});

	appWindow.listen('finder:replace-tab', async (d) => {
		if (EditorManager.mainIsFocused()) {
			let { pos, replace, find } = d.payload;
			console.log('POS', pos);
			let value = view.state.doc.slice(pos.from, pos.to).toString().toLowerCase();
			const w = new Window(d.windowLabel);
			if (value == pos.found.toLowerCase()) {
				let trans = view.state.update({
					changes: { from: pos.from, to: pos.to, insert: pos.replacement },
				});
				view.dispatch(trans);
				await w.emit('tabulater:replace-tab-success', {});
			} else {
				await w.emit('alert', {
					msg: 'Replace not possible document changed.',
					type: AlertType.ERR,
				});
			}
		}
	});

	appWindow.listen('finder:undo-tab', async (d) => {
		if (EditorManager.mainIsFocused()) {
			let { pos, replace, find } = d.payload;
			let value = view.state.doc
				.slice(pos.from, pos.from + replace.length)
				.toString()
				.toLowerCase();
			console.log('UNDO-TAB :', value, replace);
			const w = new Window(d.windowLabel);
			if (value == replace) {
				let trans = view.state.update({
					changes: { from: pos.from, to: pos.from + replace.length, insert: find },
				});
				view.dispatch(trans);
				await w.emit('tabulater:undo-tab-success', {});
			} else {
				await w.emit('alert', {
					msg: "Can't undo the change document changed.",
					type: AlertType.ERR,
				});
			}
		}
	});

	appWindow.listen('finder:tab-replace-current-set', async (d) => {
		if (EditorManager.mainIsFocused()) {
			let { positions, replace, find } = d.payload;
			let changesSpec = [];
			let isOk = true;
			for (let i = 0; i < positions.length; i++) {
				let value = view.state.doc.slice(positions[i].from, positions[i].to).toString().toLowerCase();
				console.log(value, find.toLowerCase());
				if (value != positions[i].found.toLowerCase()) {
					isOk = false;
				} else {
					changesSpec.push({ from: positions[i].from, to: positions[i].to, insert: positions[i].replacement });
				}
			}
			const w = new Window(d.windowLabel);
			if (isOk || changesSpec.length > 0) {
				let trans = view.state.update({
					changes: changesSpec,
				});
				view.dispatch(trans);
				await w.emit('tabulater:tab-replace-current-set-success', {});
			} else {
				await w.emit('alert', {
					msg: 'Document Changed replacing current set not possible',
					type: AlertType.ERR,
				});
			}
		}
	});

	appWindow.listen('finder:get-window-data', async (e) => {
		console.log('GET WINDOW DATA', e);
		let folder = await MyStorage.getFileFolder();
		console.log('>> Folder =>', folder, e.windowLabel);
		await new Window('finder').emit('finder:set-window-data', {
			path: folder,
			appTitle: await appWindow.title(),
		});
	});

	appWindow.listen('finder:closing', async () => {
		if (EditorManager.mainIsFocused()) {
			await appWindow.setFocus();
			view.focus();
		}
	});
}

async function updateFindInStore(newValue) {
	let oldData = await FinderStore.getFinderData();
	if (oldData) {
		let newData = {
			...oldData,
			find: newValue,
		};
		await FinderStore.storeFinderData(newData);
		return newData;
	}
	return null;
}

async function updateReplaceInStore(newValue) {
	let oldData = await FinderStore.getFinderData();
	if (oldData) {
		let newData = {
			...oldData,
			replace: newValue,
		};
		await FinderStore.storeFinderData(newData);
		return newData;
	}
	return null;
}

export const FindDialog = { initFinderListeners, openFinder };

// search shortcut (Mod means Ctrl in windows or cmd in Mac)
export const FinderKeyBinding = [
	{
		key: 'Mod-f',
		run: openFinder,
		shift: async () => {
			await FindTable.openTabulater(true);
		},
	},
	{
		key: 'Mod-e',
		run: async () => {
			if (!data.wrapAround && !data.selected_only) {
				let { from, to } = view.state.selection.main;
				lastAnchor = Math.min(from, to);
			}
			let findValue = Editor.getCurrentSelectionValue();
			await openSearchPanel(view);
			await emit('finder:ctrl-e', findValue);
			let finderData = await updateFindInStore(findValue);
			appWindow.emit('finder:find', finderData);
			view.focus();
		},
		shift: async () => {
			await setQueryIfClosed();
			let replaceValue = Editor.getCurrentSelectionValue();
			emit('finder:ctrl-shift-e', replaceValue);
			let finderData = await updateReplaceInStore(replaceValue);
			appWindow.emit('finder:find', finderData);
		},
	},
	{
		key: 'Mod-g',
		run: async () => {
			if (!data.selected_only) {
				await setQueryIfClosed();
				await goNext();
				runListenerIfNeeded();
				countMatches(view);
			}
		},
		shift: async () => {
			if (!data.selected_only) {
				await setQueryIfClosed();
				await goPrevious();
				runListenerIfNeeded();
				countMatches(view);
			}
		},
	},
	{
		key: 'Mod-=',
		run: async () => {
			if (!data.selected_only) {
				await setQueryIfClosed();
				replace(view);
				countMatches(view);
			}
		},
		shift: async () => {
			console.log('______________________');
			console.log('cmd+shift+= ____ START (prev)', currentSel);
			if (!data.selected_only) {
				await setQueryIfClosed();
				let csel = view.state.selection.main;
				console.log('... cmd+shift+= ____ csel', csel);
				if (
					!data.wrapAround &&
					(getCountSuffix(view) == CountSuffixEnum.TO_END.description ||
						getCountSuffix(view) == CountSuffixEnum.TO_START.description)
				) {
					let matches = getAllMatchesInDoc(view);
					let ind = matches.findIndex((s) => s.from == csel.from /* && s.to == csel.to */);
					//currentSel = {from:matches[ind>0?ind-1:0].from,to:view.state.doc.length}
					console.log('... cmd+shift+= ____ ind, matches[ind]', ind, matches[ind]);
					currentSel = { from: 0, to: matches[ind].from };
					console.log('... cmd+shift+= ____ currentSel', currentSel);
					replace(view); // i.e. replace and find next
					countSuffix = CountSuffixEnum.TO_START.description;
					await commit();
				} else replaceNext(view); // i.e. replace and find next
				findPrevious(view);
				countMatches(view);
			}
		},
	},
	{
		key: 'Mod-Alt-=',
		run: async () => {
			console.log('______________________');
			console.log('cmd+option+= ____ START (next)', currentSel);
			if (!data.selected_only) {
				await setQueryIfClosed();
				let csel = view.state.selection.main;
				if (!data.wrapAround && getCountSuffix(view) == CountSuffixEnum.TO_START.description) {
					let matches = getAllMatchesInDoc(view);
					let ind = matches.findIndex((s) => s.from == csel.from && s.to == csel.to);
					// currentSel = {from:0,to:matches[ind<matches.length-1?ind+1:ind].to}
					currentSel = { from: matches[ind].to, to: view.state.doc.length };
					replaceNext(view);
					countSuffix = CountSuffixEnum.TO_END.description;
					await commit();
					findNext(view);
				} else replaceNext(view);
				countMatches(view);
			}
		},
	},
];
