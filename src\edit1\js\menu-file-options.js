import { invoke } from '@tauri-apps/api/core';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';
import { platform } from '@tauri-apps/plugin-os';
import { view } from '../main';
import { AutoSave } from './autosave.js';
import { Editor } from './editor';
import { MyStorage } from './storage';
const appWindow = getCurrentWebviewWindow();
const plt = platform();

export function initMenuFileOptions() {
	async function saveFile() {
		var data = MyStorage.getData();
		console.log("[saveFile] MyStorage data",data);
		await invoke('editor_save_file', {
			path: data.path,
			content: Editor.getValue(),
			ext: data.ext,
		})
			.then(async (d) => {
				MyStorage.setFile(d);
				console.log("[saveFile] data",d);
				const removed = await AutoSave.removeAutosave(d.path);
				console.log("[saveFile] removed",removed);
				if(!removed) await AutoSave.removeAutosave(appWindow.label);
				// checkIfSaved();
			})
			.catch((e) => console.error(e));
	}

	async function saveFileAs() {
		var data = MyStorage.getData();
		console.log("🚀 ~ saveFileAs ~ data:", data)
		await invoke('save_as_file', {
			label: window.label ? window.label : 'main',
			path: data ? data.path : '',
			content: view.state.doc.toString(),
			ext: data ? data.ext : '',
		})
			.then((data) => {
				console.log("🚀 ~ .then ~ data:", data)
				MyStorage.setFile(data);
				// checkIfSaved();
			})
			.catch((e) => console.error(e));
	}

	appWindow.listen('file:save', async () => {
		console.log(`$ window <${appWindow.label}> saved`);
		toggleFile.checked = false;
		var data = MyStorage.getData();
		console.log('Data Storage =>', data);
		if (!!data) {
			saveFile();
		} else {
			saveFileAs();
		}
	});

	appWindow.listen('file:save-as', async () => {
		console.log(`$ window <${appWindow.label}> saved As `);
		// toggleFile.checked = false;
		saveFileAs();
	});

	window.addEventListener('keyup', async (e) => {
		console.log('Platform =>', plt);
		if ((plt == 'macos' && e.metaKey) || (plt != 'macos' && e.ctrlKey)) {
			switch (e.key) {
				case 'o':
					await invoke('open_files', {});
					break;
				case 'd':
					await invoke('open_data_files', {});
					break;
				case 's':
					var data = MyStorage.getData();
					if (data) {
						saveFile();
					} else {
						saveFileAs();
					}
					break;
				default:
					break;
			}
			if (e.shiftKey) {
				switch (e.key) {
					case 'S':
						saveFileAs();
						break;
					default:
						break;
				}
			}
		}
	});
}
