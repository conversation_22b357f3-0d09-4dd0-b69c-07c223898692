import { css } from '@codemirror/lang-css';
import { html } from '@codemirror/lang-html';
import { javascript } from '@codemirror/lang-javascript';
import { markdown } from '@codemirror/lang-markdown';
import { python } from '@codemirror/lang-python';
import { rust } from '@codemirror/lang-rust';
import { sql } from '@codemirror/lang-sql';
import { xml } from '@codemirror/lang-xml';
import { StreamLanguage } from '@codemirror/language';
import { jinja2 } from '@codemirror/legacy-modes/mode/jinja2';
import { shell } from '@codemirror/legacy-modes/mode/shell';
import { Compartment, EditorSelection } from '@codemirror/state';
import { EditorView } from '@codemirror/view';
import { coolGlow, espresso } from 'thememirror';
import { setSearchQuery } from './codemirror-search-modified';
import { EditorManager } from './editor-manager';

// let language = new Compartment();
// let theme = new Compartment();
// let lineWrap = new Compartment();

// const languages = {
// 	css: { exts: ['css'], lang: css() },
// 	xml: { exts: ['xml', 'xsl', 'rss', 'svg', 'html'], lang: xml() },
// 	html: { exts: ['html'], lang: html() },
// 	javascript: { exts: ['js'], lang: javascript() },
// 	markdown: { exts: ['md'], lang: markdown() },
// 	python: { exts: ['py'], lang: python() },
// 	rust: { exts: ['rs'], lang: rust() },
// 	sql: { exts: ['sql', 'db'], lang: sql() },
// 	jinja2: {
// 		exts: ['j2', 'jinja', 'jinja2'],
// 		lang: StreamLanguage.define(jinja2),
// 	},
// 	shell: { exts: ['sh'], lang: StreamLanguage.define(shell) },
// };
// /**
//  *
//  * @param {*} extension the extension of the opened file
//  * @descriptios set the right language support
//  */
// function setLanguageByExt(extension) {
// 	for (let language in languages) {
// 		if (languages[language].exts.includes(extension)) {
// 			setLanguage(languages[language].lang);
// 		}
// 	}
// }

// function setLanguage(lang) {
// 	view.dispatch({
// 		effects: language.reconfigure(lang),
// 	});
// }

// function setTheme(dark) {
// 	view.dispatch({
// 		effects: theme.reconfigure(dark ? coolGlow : espresso),
// 	});
// }

// function getValue() {
// 	return view.state.doc.toString();
// }

// function setValue(value) {
// 	let trans = view.state.update({
// 		changes: { from: 0, to: view.state.doc.length, insert: value },
// 	});
// 	view.dispatch(trans);
// }

// function setValueFrom(from, value) {
// 	let trans = view.state.update({
// 		changes: { from: from, insert: value },
// 	});
// 	view.dispatch(trans);
// }

// function getCurrentSelection() {
// 	return {
// 		from: view.state.selection.main.from,
// 		to: view.state.selection.main.to,
// 	};
// }

// function adjustMainViewCursorPos(from, to) {
// 	if (view) {
// 		view.focus();
// 		let trans = view.state.update({
// 			selection: EditorSelection.single(from, to),
// 		});
// 		view.dispatch(trans);
// 	}
// }

// function getCurrentSelectionValue() {
// 	let s = getCurrentSelection();
// 	return view.state.sliceDoc(s.from, s.to);
// }

// const dragAndDropExt = EditorView.domEventHandlers({
// 	dragstart(e, _) {
// 		let s = getCurrentSelection();
// 		let data = { copy: e.altKey, from: Math.min(s.from, s.to) };
// 		e.dataTransfer.setData('text/object', JSON.stringify(data));
// 	},
// 	dragover(e, _) {
// 		e.preventDefault();
// 	},
// 	drop(e, _) {
// 		let data = JSON.parse(e.dataTransfer.getData('text/object'));
// 		if (data.copy) setValueFrom(data.from, e.dataTransfer.getData('text/plain'));
// 	},
// });

// function setEffectsSearchQuery(query) {
// 	view.dispatch({
// 		effects: setSearchQuery.of(query),
// 	});
// }

// function getLineByPos(pos) {
// 	return view.state.doc.lineAt(pos);
// }

// function getLineByNumber(n) {
// 	return view.state.doc.line(n);
// }

// function getLine(pos) {
// 	return getLineByNumber(getLineByPos(pos).number);
// }

// // Selection listener Functions
// let selectionListener = new Compartment();

// function startSelectionListener(callback) {
// 	const ext = EditorView.updateListener.of((update) => {
// 		let selection = getCurrentSelection(); // only for console.log
// 		console.log(
// 			`EDITOR - docChanged ${update.docChanged} - selectionSet ${update.selectionSet} - from: ${selection.from}, to: ${selection.to}`,
// 		);
// 		if (update.docChanged || update.selectionSet) callback(update.transactions);
// 	});
// 	view.dispatch({
// 		effects: selectionListener.reconfigure(ext),
// 	});
// }

// function isSelectionListening() {
// 	return selectionListener.get(view.state) ? true : false;
// }

// function removeSelectionListener() {
// 	view.dispatch({
// 		effects: selectionListener.reconfigure(null),
// 	});
// }

// function setFocusColorEffect(hasFocus) {
// 	const editor = document.querySelector('#editor');
// 	const gutters = document.querySelector('#editor .cm-gutters');
// 	const content = document.querySelector('#editor .cm-content');
// 	const searchMatches = document.querySelector('#editor .search-matches');
// 	console.log('Gutters =>', gutters);
// 	if (gutters) {
// 		if (hasFocus) {
// 			editor.classList.add("active");
// 			gutters.classList.remove('blured');
// 			content.classList.remove('blured');
// 			if (searchMatches) searchMatches.style.display = 'flex';
// 			EditorManager.setFocusedEditor('main');
// 		} else {
// 			editor.classList.remove("active");
// 			gutters.classList.add('blured');
// 			content.classList.add('blured');
// 			if (searchMatches) searchMatches.style.display = 'none';
// 		}
// 	}
// }

// function isFocused() {
// 	return view?view.hasFocus:false;
// }

// function toggleLineWrapping(view) {
// 	const current = view.state.facet(EditorView.lineWrapping)
// 	const newExtension = current ? [] : [EditorView.lineWrapping]

// 	view.dispatch({
// 		effects: lineWrap.reconfigure(newExtension)
// 	})

// 	return !current
// }


// export const Editor = {
// 	dragAndDropExt,
// 	language,
// 	theme,
// 	lineWrap,
// 	selectionListener,
// 	getValue,
// 	setValue,
// 	setLanguageByExt,
// 	getCurrentSelection,
// 	getCurrentSelectionValue,
// 	setEffectsSearchQuery,
// 	setTheme,
// 	getLine,
// 	startSelectionListener,
// 	removeSelectionListener,
// 	isSelectionListening,
// 	adjustMainViewCursorPos,
// 	setFocusColorEffect,
// 	isFocused,
// 	toggleLineWrapping
// };



export class EditorController {
    constructor() {
        this.language = new Compartment();
        this.theme = new Compartment();
        this.lineWrap = new Compartment();
        this.selectionListener = new Compartment();

        this.languages = {
            css: { exts: ['css'], lang: css() },
            xml: { exts: ['xml', 'xsl', 'rss', 'svg', 'html'], lang: xml() },
            html: { exts: ['html'], lang: html() },
            javascript: { exts: ['js'], lang: javascript() },
            markdown: { exts: ['md'], lang: markdown() },
            python: { exts: ['py'], lang: python() },
            rust: { exts: ['rs'], lang: rust() },
            sql: { exts: ['sql', 'db'], lang: sql() },
            jinja2: {
                exts: ['j2', 'jinja', 'jinja2'],
                lang: StreamLanguage.define(jinja2),
            },
            shell: { exts: ['sh'], lang: StreamLanguage.define(shell) },
        };

        this.dragAndDropExt = EditorView.domEventHandlers({
            dragstart: (e, _) => this.onDragStart(e),
            dragover: (e, _) => this.onDragOver(e),
            drop: (e, _) => this.onDrop(e),
        });
    }

	attachView(view){
		this.view = view;
	}

    setLanguageByExt(extension) {
        for (let langName in this.languages) {
            if (this.languages[langName].exts.includes(extension)) {
                this.setLanguage(this.languages[langName].lang);
            }
        }
    }

    setLanguage(lang) {
        this.view.dispatch({
            effects: this.language.reconfigure(lang),
        });
    }

    setTheme(dark) {
        this.view.dispatch({
            effects: this.theme.reconfigure(dark ? coolGlow : espresso),
        });
    }

    getValue() {
        return this.view.state.doc.toString();
    }

    setValue(value) {
        let trans = this.view.state.update({
            changes: { from: 0, to: this.view.state.doc.length, insert: value },
        });
        this.view.dispatch(trans);
    }

    setValueFrom(from, value) {
        let trans = this.view.state.update({
            changes: { from, insert: value },
        });
        this.view.dispatch(trans);
    }

    getCurrentSelection() {
        return {
            from: this.view.state.selection.main.from,
            to: this.view.state.selection.main.to,
        };
    }

    adjustMainViewCursorPos(from, to) {
        if (this.view) {
            this.view.focus();
            let trans = this.view.state.update({
                selection: EditorSelection.single(from, to),
            });
            this.view.dispatch(trans);
        }
    }

    getCurrentSelectionValue() {
        let s = this.getCurrentSelection();
        return this.view.state.sliceDoc(s.from, s.to);
    }

    onDragStart(e) {
        let s = this.getCurrentSelection();
        let data = { copy: e.altKey, from: Math.min(s.from, s.to) };
        e.dataTransfer.setData('text/object', JSON.stringify(data));
    }

    onDragOver(e) {
        e.preventDefault();
    }

    onDrop(e) {
        let data = JSON.parse(e.dataTransfer.getData('text/object'));
        if (data.copy) this.setValueFrom(data.from, e.dataTransfer.getData('text/plain'));
    }

    setEffectsSearchQuery(query) {
        this.view.dispatch({
            effects: setSearchQuery.of(query),
        });
    }

    getLineByPos(pos) {
        return this.view.state.doc.lineAt(pos);
    }

    getLineByNumber(n) {
        return this.view.state.doc.line(n);
    }

    getLine(pos) {
        return this.getLineByNumber(this.getLineByPos(pos).number);
    }

    startSelectionListener(callback) {
        const ext = EditorView.updateListener.of((update) => {
            let selection = this.getCurrentSelection();
            console.log(
                `EDITOR - docChanged ${update.docChanged} - selectionSet ${update.selectionSet} - from: ${selection.from}, to: ${selection.to}`
            );
            if (update.docChanged || update.selectionSet) callback(update.transactions);
        });
        this.view.dispatch({
            effects: this.selectionListener.reconfigure(ext),
        });
    }

    isSelectionListening() {
        return !!this.selectionListener.get(this.view.state);
    }

    removeSelectionListener() {
        this.view.dispatch({
            effects: this.selectionListener.reconfigure(null),
        });
    }

    setFocusColorEffect(hasFocus) {
        const editor = document.querySelector('#editor');
        const gutters = document.querySelector('#editor .cm-gutters');
        const content = document.querySelector('#editor .cm-content');
        const searchMatches = document.querySelector('#editor .search-matches');
        console.log('Gutters =>', gutters);
        if (gutters) {
            if (hasFocus) {
                editor.classList.add("active");
                gutters.classList.remove('blured');
                content.classList.remove('blured');
                if (searchMatches) searchMatches.style.display = 'flex';
                EditorManager.setFocusedEditor('main');
            } else {
                editor.classList.remove("active");
                gutters.classList.add('blured');
                content.classList.add('blured');
                if (searchMatches) searchMatches.style.display = 'none';
            }
        }
    }

    isFocused() {
        return this.view ? this.view.hasFocus : false;
    }

    setLineWrapping(value) {
        const newExtension = value ?  [EditorView.lineWrapping]:[];
        this.view.dispatch({
            effects: this.lineWrap.reconfigure(newExtension)
        });
    }
}


export const Editor = new EditorController();
